package com.solum.xplain.shared.datagrid.impl.hazelcast

import com.hazelcast.config.Config
import com.hazelcast.core.Hazelcast
import com.hazelcast.core.HazelcastInstance
import com.hazelcast.cp.CPSubsystem
import com.hazelcast.cp.lock.FencedLock
import com.hazelcast.cp.lock.exception.LockOwnershipLostException
import java.util.concurrent.TimeUnit
import org.junit.jupiter.api.Disabled
import spock.lang.IgnoreIf
import spock.lang.Specification

@IgnoreIf(value = {
  env['CI'] == 'true'
}, reason = "Flaky in CI. May also fail locally when using gradle test --parallel")
class HazelcastClusterLockIntegrationTest extends Specification {

  private HazelcastInstance instance1
  private HazelcastInstance instance2
  private static final String LOCK_NAME = "test-lock"

  def setup() {
    // Configure CP subsystem with minimal setup
    def config = new Config()
    config.getCPSubsystemConfig().setCPMemberCount(3)

    // Start two separate Hazelcast instances
    instance1 = Hazelcast.newHazelcastInstance(config)
    instance2 = Hazelcast.newHazelcastInstance(config)

    // Add a third instance to satisfy CP member count
    Hazelcast.newHazelcastInstance(config)
  }

  def cleanup() {
    Hazelcast.shutdownAll()
  }

  def "should support reentrant locking"() {
    given: "a cluster lock from the first instance"
    CPSubsystem cpSubsystem = instance1.getCPSubsystem()
    FencedLock fencedLock = cpSubsystem.getLock(LOCK_NAME)
    def lock = new HazelcastClusterLock(fencedLock)

    when: "acquiring the lock for the first time"
    def firstLockResult = lock.tryLock()

    then: "it should succeed"
    firstLockResult

    when: "acquiring the lock again from the same instance"
    def secondLockResult = lock.tryLock()

    then: "it should succeed (reentrant)"
    secondLockResult

    cleanup:
    lock.unlock()
    lock.unlock()
  }

  def "should provide exclusive locking across instances"() {
    given: "locks from both instances"
    def lock1 = new HazelcastClusterLock(instance1.getCPSubsystem().getLock(LOCK_NAME))
    def lock2 = new HazelcastClusterLock(instance2.getCPSubsystem().getLock(LOCK_NAME))

    when: "first instance acquires the lock"
    def lock1Result = lock1.tryLock()

    then: "it should succeed"
    lock1Result

    when: "second instance tries to acquire the same lock"
    def lock2Result = lock2.tryLock(1, TimeUnit.SECONDS)

    then: "it should fail"
    !lock2Result

    when: "first instance releases the lock"
    lock1.unlock()

    and: "second instance tries again"
    def lock2ResultAfterUnlock = lock2.tryLock(1, TimeUnit.SECONDS)

    then: "it should succeed"
    lock2ResultAfterUnlock

    cleanup:
    if (lock2ResultAfterUnlock) {
      lock2.unlock()
    }
  }

  def "should demonstrate fencing behavior after instance failure"() {
    given: "a lock from the first instance"
    def lock1 = new HazelcastClusterLock(instance1.getCPSubsystem().getLock(LOCK_NAME))

    when: "first instance acquires the lock"
    def lock1Result = lock1.tryLock()

    then: "it should succeed"
    lock1Result

    when: "simulating network partition by shutting down instance1"
    instance1.shutdown()

    and: "creating a new lock from the second instance"
    def lock2 = new HazelcastClusterLock(instance2.getCPSubsystem().getLock(LOCK_NAME))

    then: "after session timeout, lock2 should be able to acquire the lock"
    def acquired = false
    for (int i = 0; i < 30 && !acquired; i++) {
      acquired = lock2.tryLock(1, TimeUnit.SECONDS)
      if (!acquired) {
        sleep(1000)
      }
    }

    acquired

    cleanup:
    if (acquired) {
      lock2.unlock()
    }
  }

  def "should demonstrate fencing behaviour on reentrant use of lock1 after expiry has allowed lock2 on instance2 to acquire lock "() {
    given: "a lock from the first instance"

    def fencedLock1 = instance1.getCPSubsystem().getLock(LOCK_NAME)
    def lock1 = new HazelcastClusterLock(fencedLock1)

    when: "first instance acquires the lock"
    def lock1Result = lock1.tryLock()

    then: "it should succeed"
    lock1Result

    when: "close all sessions on instance1"
    instance1.getCPSubsystem().getCPSessionManagementService().getAllSessions(fencedLock1.groupId.getName()).thenApply { sessions ->
      sessions.forEach { session ->
        instance1.getCPSubsystem().getCPSessionManagementService().forceCloseSession(fencedLock1.groupId.getName(), session.id() )
      }
    }

    and: "second instance tries to acquire the lock"
    def lock2 = new HazelcastClusterLock(instance2.getCPSubsystem().getLock(LOCK_NAME))
    def lock2Result = lock2.tryLock(1, TimeUnit.SECONDS)

    then: "it should succeed"
    lock2Result

    when: "first instance tries to re-enter the lock"
    lock1.tryLock(1, TimeUnit.SECONDS)

    then: "it should fail"
    LockOwnershipLostException e = thrown()
    e.toString() == "com.hazelcast.cp.lock.exception.LockOwnershipLostException: Current thread is not owner of the Lock[test-lock] because its Session[1] is closed by server!"

    cleanup:
    if (lock2Result) {
      lock2.unlock()
    }
  }
}
