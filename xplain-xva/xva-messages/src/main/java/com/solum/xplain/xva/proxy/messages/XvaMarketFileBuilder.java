package com.solum.xplain.xva.proxy.messages;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;

public class XvaMarketFileBuilder {
  public static final String ANCHOR_DATE = "AnchorDate";
  public static final String NUMERAIRE = "Numeraire";
  public static final String COLLATERAL_CCY = "CollateralCcy";
  private final String baseCcy;
  private final ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();

  public XvaMarketFileBuilder(String baseCcy) {
    this.baseCcy = baseCcy;
    builder.put(NUMERAIRE, baseCcy);
    builder.put(COLLATERAL_CCY, baseCcy);
    builder.put("Inflations", Collections.emptyList());
  }

  public XvaMarketFile build() {
    return new XvaMarketFile(builder.build());
  }

  public XvaMarketFileBuilder anchorDate(LocalDate date) {
    builder.put(ANCHOR_DATE, date);
    return this;
  }

  public XvaMarketFileBuilder currencies(Collection<String> currencies) {
    builder.put("Currencies", ImmutableList.copyOf(currencies));
    return this;
  }

  public XvaMarketFileBuilder credits(Set<String> counterparties) {
    builder.put("Credits", counterparties);
    return this;
  }

  public XvaMarketFileBuilder tstar(long timeEnd) {
    builder.put("TStar", timeEnd);
    return this;
  }

  public XvaMarketFileBuilder settings(int sigmaStep, double hullWhiteMeanReversion) {
    builder.put("SigmaStep", sigmaStep);
    builder.put("HWRevert", hullWhiteMeanReversion);
    return this;
  }

  public XvaMarketFileBuilder fxSpot(FxSpotValue fxSpotValue) {
    builder.put("FxSpot", fxSpotValue);
    return this;
  }

  @Deprecated
  public XvaMarketFileBuilder fxVol(String currencyPair, FxVolValue fxVolValue) {
    builder.put("FxVol_" + fixCurrencyOrder(currencyPair), fxVolValue);
    return this;
  }

  public XvaMarketFileBuilder fxVolWithSlash(String currencyPair, FxVolValue fxVolValue) {
    builder.put("FxVol_" + fixCurrencyOrderWithSlash(currencyPair), fxVolValue);
    return this;
  }

  private String fixCurrencyOrder(String currencyPair) {
    if (currencyPair.length() == 6) {
      String ccy1 = currencyPair.substring(0, 3);
      String ccy2 = currencyPair.substring(3, 6);
      if (ccy1.equals(baseCcy)) {
        return ccy2 + ccy1;
      } else {
        return ccy1 + ccy2;
      }
    } else {
      return currencyPair;
    }
  }

  private String fixCurrencyOrderWithSlash(String currencyPair) {
    if (currencyPair.length() == 7) {
      String ccy1 = currencyPair.substring(0, 3);
      String ccy2 = currencyPair.substring(4, 7);
      if (ccy1.equals(baseCcy)) {
        return ccy2 + ccy1;
      } else {
        return ccy1 + ccy2;
      }
    } else {
      return currencyPair;
    }
  }

  public XvaMarketFileBuilder correlations(CorrelationsValue correlations) {
    builder.put("Correlations_" + baseCcy, correlations);
    return this;
  }

  public XvaMarketFileBuilder discountFactorsFloating(String currency, DiscountFactorsValue value) {
    builder.put("DiscountFactorsFloating_" + currency, value);
    return this;
  }

  public XvaMarketFileBuilder discountFactorsFunding(String currency, DiscountFactorsValue value) {
    builder.put("DiscountFactorsFunding_" + currency, value);
    return this;
  }

  public XvaMarketFileBuilder swaptionVols(String currency, SwaptionVolatilitiesValue value) {
    builder.put("SwaptionVols_" + currency, value);
    return this;
  }

  public XvaMarketFileBuilder fundingSpreads(FundingSpreadsValue fundingSpreads) {
    builder.put("FundingSpreads", fundingSpreads);
    return this;
  }

  public XvaMarketFileBuilder creditValue(CreditValue creditValue) {
    builder.put("Credit_" + creditValue.getParty(), creditValue);
    return this;
  }

  public XvaMarketFileBuilder irBasisSpreads(String currency, IRBasisSpreadsValue basisSpreadsValue) {
    builder.put("IRBasisSpreads_" + currency, basisSpreadsValue);
    return this;
  }
}
