package com.solum.xplain.xm.excmngmt.processipv.view.viewconfig;

import static com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName.BREAK_AGE;
import static com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName.THRESHOLD_LEVEL;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnDefinitionGroupBuilder;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldType;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.TradeResultView;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * This builder creates a labelled column definition group view, by adding columns if field names
 * match a predicate. Another predicate determines if the column is hidden when the group is
 * collapsed.
 */
class ColumnDefinitionGroupBuilder extends AbstractColumnDefinitionGroupBuilder {
  public ColumnDefinitionGroupBuilder(
      Predicate<String> matcher, String label, Predicate<String> hidden) {
    super(matcher, label, hidden);
  }

  public ColumnDefinitionGroupBuilder(
      Predicate<String> matcher,
      String label,
      Predicate<String> hiddenWhenCollapsed,
      Predicate<String> hidden) {
    super(matcher, label, hiddenWhenCollapsed, hidden);
  }

  /**
   * Compile a test based on a regular expression which matches any field name ending in
   * par_rate_suffix. To be used later to override the default value of 2 decimal places for par
   * rate columns
   */
  private static final Predicate<String> PAR_RATE_PREDICATE =
      Pattern.compile(IpvTradeOverlayResultView.FieldName.PAR_RATE_SUFFIX + "$").asPredicate();

  /**
   * Simple implementation to determine the default precision for a field.
   *
   * <p>Numeric fields have a default precision of 5 decimal places, except for {@value
   * com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName#THRESHOLD_LEVEL}
   * and {@value TradeResultView.Fields#notional} which have a precision of 0, and provider fields
   * which have a precision of 2. Non-numeric fields have a null precision.
   *
   * <p>Numeric fields such as Par Rate should be displayed as real numbers to be in line with other
   * parts of Xplain, and therefore keep the 5 decimal places
   *
   * @param field the field to return precision for.
   * @return precision in decimal places, or null if a non-numeric field
   */
  @Override
  protected Integer precision(FieldDefinitionView field) {
    if (field.type() == FieldType.NUMBER) {
      return switch (field.name()) {
        case THRESHOLD_LEVEL, TradeResultView.Fields.notional, BREAK_AGE -> 0;
        default -> PAR_RATE_PREDICATE.test(field.name()) ? 5 : 2;
      };
    }
    return null;
  }
}
