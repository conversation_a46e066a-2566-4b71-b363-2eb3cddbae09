package com.solum.xplain.xm.excmngmt.processipv.view.viewconfig;

import static com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName.*;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnsBuilder;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultBreakView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultResolutionView;
import com.solum.xplain.xm.excmngmt.processipv.view.TradeResultView;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * The default view includes all fields in the palette. Any fields which are not in the Break Test,
 * or Resolution Summary groups and do not match primary, secondary, etc patterns, are added to the
 * Trade Details group. This way we ensure the default view includes all possible fields.
 */
class DefaultColumnsBuilder extends AbstractColumnsBuilder {
  static final Set<String> RESOLUTION_SUMMARY_FIELDS =
      Set.of(
          IpvTradeOverlayResultView.Fields.status,
          IpvTradeResultResolutionView.Fields.resolution,
          RESOLUTION_VALUE,
          VERIFICATION_RESOLUTION,
          IpvTradeResultResolutionView.Fields.resolutionComment,
          RESOLUTION_EVIDENCE,
          IpvTradeResultResolutionView.Fields.approvalComment,
          APPROVAL_EVIDENCE);
  private static final Set<String> DEFAULT_BREAK_TEST_FIELDS =
      Set.of(
          THRESHOLD_LEVEL,
          IpvTradeResultBreakView.Fields.breakTestName,
          BREAK_AGE,
          BREAK_TEST_COLUMNS,
          IpvTradeResultBreakView.Fields.breakTestType,
          TEST_VALUE,
          IpvTradeResultBreakView.Fields.measureType,
          IpvTradeResultBreakView.Fields.threshold,
          IpvTradeResultBreakView.Fields.operator,
          IpvTradeResultBreakView.Fields.parentBreakTestName,
          PARENT_BREAK_TEST_PROVIDER);
  private static final Set<String> ACCOUNTING_DEAL_COST_FIELDS =
      Set.of(TradeResultView.Fields.accountingCost, TradeResultView.Fields.dealCost);
  private static final Predicate<String> PRIMARY_PROVIDER_PREDICATE =
      Pattern.compile("^primary[A-Z]").asPredicate();
  private static final Predicate<String> SECONDARY_PROVIDER_PREDICATE =
      Pattern.compile("^secondary[A-Z]").asPredicate();
  private static final Predicate<String> TERTIARY_PROVIDER_PREDICATE =
      Pattern.compile("^tertiary[A-Z]").asPredicate();
  private static final Predicate<String> QUATERNARY_PROVIDER_PREDICATE =
      Pattern.compile("^quaternary[A-Z]").asPredicate();
  private static final Predicate<String> TRADE_DETAILS_GROUP_MATCHER =
      Predicate.not(
          name ->
              DEFAULT_BREAK_TEST_FIELDS.contains(name)
                  || RESOLUTION_SUMMARY_FIELDS.contains(name)
                  || ACCOUNTING_DEAL_COST_FIELDS.contains(name)
                  || PRIMARY_PROVIDER_PREDICATE.test(name)
                  || SECONDARY_PROVIDER_PREDICATE.test(name)
                  || TERTIARY_PROVIDER_PREDICATE.test(name)
                  || QUATERNARY_PROVIDER_PREDICATE.test(name));
  private static final Set<String> DEFAULT_HIDDEN_FIELDS =
      Set.of(
          COMPANY_ID,
          ENTITY_ID,
          TradeResultView.Fields.underlying,
          START_DATE,
          END_DATE,
          EXPIRY,
          CCY,
          TradeResultView.Fields.notional,
          IpvTradeResultBreakView.Fields.breakTestType,
          TEST_VALUE,
          IpvTradeResultBreakView.Fields.measureType,
          IpvTradeResultBreakView.Fields.threshold,
          IpvTradeResultBreakView.Fields.operator,
          IpvTradeResultBreakView.Fields.parentBreakTestName,
          PARENT_BREAK_TEST_PROVIDER,
          IpvTradeResultResolutionView.Fields.resolutionComment,
          RESOLUTION_EVIDENCE,
          IpvTradeResultResolutionView.Fields.approvalComment,
          APPROVAL_EVIDENCE);

  public DefaultColumnsBuilder() {
    super(
        List.of(
            builder(TRADE_DETAILS_GROUP_MATCHER, "Trade Details"),
            builder(DEFAULT_BREAK_TEST_FIELDS::contains, "Break Test"),
            builder(RESOLUTION_SUMMARY_FIELDS::contains, "Resolution Summary"),
            builder(PRIMARY_PROVIDER_PREDICATE, "Primary Provider"),
            builder(SECONDARY_PROVIDER_PREDICATE, "Secondary Provider"),
            builder(TERTIARY_PROVIDER_PREDICATE, "Tertiary Provider"),
            builder(QUATERNARY_PROVIDER_PREDICATE, "Quaternary Provider"),
            builderHidden(ACCOUNTING_DEAL_COST_FIELDS::contains, "Accounting & Deal Cost")));
  }

  private static ColumnDefinitionGroupBuilder builder(Predicate<String> matcher, String label) {
    return new ColumnDefinitionGroupBuilder(matcher, label, DEFAULT_HIDDEN_FIELDS::contains);
  }

  private static ColumnDefinitionGroupBuilder builderHidden(
      Predicate<String> matcher, String label) {
    return new ColumnDefinitionGroupBuilder(matcher, label, name -> false, name -> true);
  }
}
