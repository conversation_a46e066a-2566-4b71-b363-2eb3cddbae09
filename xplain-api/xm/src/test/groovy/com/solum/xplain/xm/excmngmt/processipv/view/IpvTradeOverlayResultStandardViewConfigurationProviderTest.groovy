package com.solum.xplain.xm.excmngmt.processipv.view

import static com.solum.xplain.core.viewconfig.provider.ViewConfigurationProvider.DEFAULT_VIEW_NAME
import static com.solum.xplain.core.viewconfig.provider.ViewConfigurationProvider.MINIMAL_VIEW_NAME

import com.solum.xplain.core.viewconfig.PaletteProviderSpecification
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView
import com.solum.xplain.core.viewconfig.value.FieldType
import com.solum.xplain.core.viewconfig.value.PaletteView
import com.solum.xplain.xm.excmngmt.processipv.view.viewconfig.IpvTradeOverlayResultStandardViewConfigurationProvider
import spock.lang.Unroll

class IpvTradeOverlayResultStandardViewConfigurationProviderTest extends PaletteProviderSpecification {

  @Unroll
  def "should provide view configurations for #clazz"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)

    then:
    assert null != viewConfigs.find {
      it.scope().viewClass() == clazz
    }

    where:
    clazz << [IpvTradeOverlayResultView]
  }

  @Unroll
  def "should provide view configuration named #name with id #id"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)

    then:
    viewConfigs.find { it.name() == name }.id() == id

    where:
    name              | id
    DEFAULT_VIEW_NAME | "66290f6f3d0c5109c3f62507"
    MINIMAL_VIEW_NAME | "66290f703d0c5109c3f62508"
  }

  def "default view should include expected fields and groups"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)
    def extra = new FieldDefinitionView("extra", FieldType.STRING, "extra", null, true, null, null, null)
    paletteService.findPaletteView(IpvTradeOverlayResultView) >> {
      Optional<PaletteView> view = callRealMethod()
      return view.map { new PaletteView<>(it.scope(), it.fieldDefinitions() + extra) }
    }

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)
    def overlayConfig = viewConfigs.find { it.scope().viewClass() == IpvTradeOverlayResultView && it.name() == DEFAULT_VIEW_NAME }

    then:
    assert null != overlayConfig?.columnDefinitionGroups()?.find { it.name() == groupName }?.columnDefinitions()?.find { it.fieldDefinition().name() == fieldName }

    and: "extra column is added to Trade Details group"
    assert null != overlayConfig?.columnDefinitionGroups()?.find { it.name() == "Trade Details" }?.columnDefinitions()?.find { it.fieldDefinition().name() == "extra" }

    where:
    fieldName                 | groupName
    "pricingSlot"             | "Trade Details"
    "slaDeadline"             | "Trade Details"
    "portfolioId"             | "Trade Details"
    "tradeId"                 | "Trade Details"
    "type"                    | "Trade Details"
    "companyId"               | "Trade Details"
    "entityId"                | "Trade Details"
    "underlying"              | "Trade Details"
    "startDate"               | "Trade Details"
    "endDate"                 | "Trade Details"
    "expiry"                  | "Trade Details"
    "ccy"                     | "Trade Details"
    "notional"                | "Trade Details"
    "accountingCost"          | "Accounting & Deal Cost"
    "dealCost"                | "Accounting & Deal Cost"
    "maxTriggeredThresholdLevel"| "Break Test"
    "breakTestName"           | "Break Test"
    "breakAge"                | "Break Test"
    "breakTestColumns"        | "Break Test"
    "breakTestType"           | "Break Test"
    "testValue"               | "Break Test"
    "measureType"             | "Break Test"
    "threshold"               | "Break Test"
    "operator"                | "Break Test"
    "parentBreakTestName"     | "Break Test"
    "parentBreakTestProvider" | "Break Test"
    "status"                  | "Resolution Summary"
    "resolution"              | "Resolution Summary"
    "resolutionValue"         | "Resolution Summary"
    "verificationResolution"  | "Resolution Summary"
    "resolutionComment"       | "Resolution Summary"
    "resolutionEvidence"      | "Resolution Summary"
    "approvalComment"         | "Resolution Summary"
    "approvalEvidence"        | "Resolution Summary"
    "primaryProvider"         | "Primary Provider"
    "primaryPv"               | "Primary Provider"
    "primaryPreviousPv"       | "Primary Provider"
    "primaryPvDelta"          | "Primary Provider"
    "primaryGreeks"           | "Primary Provider"
    "primaryVega"             | "Primary Provider"
    "secondaryProvider"       | "Secondary Provider"
    "secondaryPv"             | "Secondary Provider"
    "secondaryPreviousPv"     | "Secondary Provider"
    "secondaryPvDelta"        | "Secondary Provider"
    "secondaryGreeks"         | "Secondary Provider"
    "secondaryVega"           | "Secondary Provider"
    "tertiaryProvider"        | "Tertiary Provider"
    "tertiaryPv"              | "Tertiary Provider"
    "tertiaryPreviousPv"      | "Tertiary Provider"
    "tertiaryPvDelta"         | "Tertiary Provider"
    "tertiaryGreeks"          | "Tertiary Provider"
    "tertiaryVega"            | "Tertiary Provider"
    "quaternaryProvider"      | "Quaternary Provider"
    "quaternaryPv"            | "Quaternary Provider"
    "quaternaryPreviousPv"    | "Quaternary Provider"
    "quaternaryPvDelta"       | "Quaternary Provider"
    "quaternaryGreeks"        | "Quaternary Provider"
    "quaternaryVega"          | "Quaternary Provider"
  }

  def "minimal view should include expected fields and groups"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)
    def overlayConfig = viewConfigs.find { it.scope().viewClass() == IpvTradeOverlayResultView && it.name() == IpvTradeOverlayResultStandardViewConfigurationProvider.MINIMAL_VIEW_NAME }

    then:
    assert null != overlayConfig?.columnDefinitionGroups()?.find { it.name() == groupName }?.columnDefinitions()?.find { it.fieldDefinition().name() == fieldName }

    where:
    fieldName                     | groupName
    "portfolioId"                 | "Position Details"
    "tradeId"                     | "Position Details"
    "type"                        | "Position Details"
    "underlying"                  | "Position Details"
    "notional"                    | "Position Details"
    "accountingCost"              | "Position Details"
    "dealCost"                    | "Position Details"
    "breakAge"                    | "Position Details"
    "primaryPv"                   | "Valuation Data"
    "secondaryPv"                 | "Valuation Data"
    "primaryGreeks"               | "Valuation Data"
    "primaryVega"                 | "Valuation Data"
    "maxTriggeredThresholdLevel"  | "Break Test"
    "breakTestName"               | "Break Test"
    "breakTestColumns"            | "Break Test"
    "testValue"                   | "Break Test"
    "parentBreakTestProvider"     | "Break Test"
    "status"                      | "Resolution Summary"
    "resolution"                  | "Resolution Summary"
    "resolutionValue"             | "Resolution Summary"
    "verificationResolution"      | "Resolution Summary"
    "resolutionComment"           | "Resolution Summary"
    "resolutionEvidence"          | "Resolution Summary"
    "approvalComment"             | "Resolution Summary"
    "approvalEvidence"            | "Resolution Summary"
  }

  def "numeric field precision should be set correctly"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)
    def overlayConfig = viewConfigs.find { it.scope().viewClass() == IpvTradeOverlayResultView && it.name() == DEFAULT_VIEW_NAME }
    def column = overlayConfig?.columnDefinitionGroups()?.find { it.name() == groupName }?.columnDefinitions()?.find { it.fieldDefinition().name() == fieldName }

    then:
    assert null != column
    assert column.precision() == precision

    where:
    fieldName                     | groupName                      | precision
    "notional"                    | "Trade Details"                | 0
    "accountingCost"              | "Accounting & Deal Cost"       | 2
    "dealCost"                    | "Accounting & Deal Cost"       | 2
    "breakAge"                    | "Break Test"                   | 0
    "testValue"                   | "Break Test"                   | 2
    "threshold"                   | "Break Test"                   | 2
    "resolutionValue"             | "Resolution Summary"           | 2
    "primaryPv"                   | "Primary Provider"             | 2
    "primaryPreviousPv"           | "Primary Provider"             | 2
    "primaryPvDelta"              | "Primary Provider"             | 2
    "primaryGreeks"               | "Primary Provider"             | 2
    "primaryPreviousGreeks"       | "Primary Provider"             | 2
    "primaryParRate"              | "Primary Provider"             | 5
    "primaryVega"                 | "Primary Provider"             | 2
    "secondaryPv"                 | "Secondary Provider"           | 2
    "secondaryPreviousPv"         | "Secondary Provider"           | 2
    "secondaryPvDelta"            | "Secondary Provider"           | 2
    "secondaryGreeks"             | "Secondary Provider"           | 2
    "secondaryPreviousGreeks"     | "Secondary Provider"           | 2
    "secondaryParRate"            | "Secondary Provider"           | 5
    "secondaryVega"               | "Secondary Provider"           | 2
    "tertiaryPv"                  | "Tertiary Provider"            | 2
    "tertiaryPreviousPv"          | "Tertiary Provider"            | 2
    "tertiaryPvDelta"             | "Tertiary Provider"            | 2
    "tertiaryGreeks"              | "Tertiary Provider"            | 2
    "tertiaryPreviousGreeks"      | "Tertiary Provider"            | 2
    "tertiaryParRate"             | "Tertiary Provider"            | 5
    "tertiaryVega"                | "Tertiary Provider"            | 2
    "quaternaryPv"                | "Quaternary Provider"          | 2
    "quaternaryPreviousPv"        | "Quaternary Provider"          | 2
    "quaternaryPvDelta"           | "Quaternary Provider"          | 2
    "quaternaryGreeks"            | "Quaternary Provider"          | 2
    "quaternaryPreviousGreeks"    | "Quaternary Provider"          | 2
    "quaternaryParRate"           | "Quaternary Provider"          | 5
    "quaternaryVega"              | "Quaternary Provider"          | 2
  }

  def "accounting and deal cost fields should be in hidden group"() {
    given:
    def provider = new IpvTradeOverlayResultStandardViewConfigurationProvider(paletteService)

    when:
    def viewConfigs = provider.provideViewConfigurations(null, IpvTradeOverlayResultView)
    def overlayConfig = viewConfigs.find {
      it.scope().viewClass() == IpvTradeOverlayResultView &&
        it.name() == DEFAULT_VIEW_NAME
    }
    def accountingDealCostGroup = overlayConfig?.columnDefinitionGroups()?.find {
      it.name() == "Accounting & Deal Cost"
    }

    then: "group should exist and contain both fields"
    accountingDealCostGroup != null
    def columns = accountingDealCostGroup.columnDefinitions()
    columns.any { it.fieldDefinition().name() == "accountingCost" }
    columns.any { it.fieldDefinition().name() == "dealCost" }

    and: "all columns in the group should be hidden"
    columns.every { !it.shown() }

    and: "fields should not appear in Trade Details group"
    def tradeDetailsGroup = overlayConfig?.columnDefinitionGroups()?.find {
      it.name() == "Trade Details"
    }
    !tradeDetailsGroup?.columnDefinitions()?.any {
      it.fieldDefinition().name() in ["accountingCost", "dealCost"]
    }
  }
}
