package com.solum.xplain.core.viewconfig.provider;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupView;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import java.util.function.Predicate;
import lombok.RequiredArgsConstructor;

/**
 * This builder creates a labelled column definition group view, by adding columns if field names
 * match a predicate. One predicate determines if the column is hidden by default, another
 * determines if the column is hidden when the group is collapsed.
 */
@RequiredArgsConstructor
public abstract class AbstractColumnDefinitionGroupBuilder {
  private final ImmutableList.Builder<ColumnDefinitionView> columns = new ImmutableList.Builder<>();
  private final Predicate<String> matcher;
  private final String label;
  private final Predicate<String> hiddenWhenCollapsed;
  private final Predicate<String> hidden;

  protected AbstractColumnDefinitionGroupBuilder(
      Predicate<String> matcher, String label, Predicate<String> hiddenWhenCollapsed) {
    this(matcher, label, hiddenWhenCollapsed, name -> false);
  }

  /**
   * Check if the field name matches the {@code matcher} predicate, and if it does then add a column
   * for it to this group.
   *
   * <p>The {@link ColumnDefinitionView#precision()} is set on the column using {@link
   * #precision(FieldDefinitionView)}. The {@link ColumnDefinitionView#shown()} flag is set using
   * the {@link #hidden} predicate. The {@link ColumnDefinitionView#shownWhenGroupCollapsed()} flag
   * is set using the {@link #hiddenWhenCollapsed} predicate.
   *
   * @param field the field to check and add a column for
   * @return true if the column was added or false if the field did not match the predicate
   */
  boolean tryAddField(FieldDefinitionView field) {
    if (matcher.test(field.name())) {
      boolean shown = !hidden.test(field.name());
      boolean shownWhenCollapsed = !hiddenWhenCollapsed.test(field.name());
      Integer precision = precision(field);

      columns.add(new ColumnDefinitionView(field, shown, shownWhenCollapsed, null, precision));
      return true;
    }
    return false;
  }

  protected abstract Integer precision(FieldDefinitionView field);

  /**
   * Create a new {@link ColumnDefinitionGroupView} using the columns added to this builder.
   *
   * @return the built column definition group
   */
  ColumnDefinitionGroupView build() {
    return new ColumnDefinitionGroupView(label, columns.build());
  }
}
