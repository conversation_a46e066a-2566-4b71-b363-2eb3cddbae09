package com.solum.xplain.calculation;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.CALCULATION_RESULTS_FIELDS;
import static java.util.stream.Collectors.toSet;

import com.solum.xplain.calculation.csv.PortfolioItemResultCsvMapper;
import com.solum.xplain.calculation.events.CalculationCanceledEvent;
import com.solum.xplain.calculation.events.CalculationDeletedEvent;
import com.solum.xplain.calculation.repository.CalculationResultRepository;
import com.solum.xplain.calculation.repository.CalculationTotalsRepository;
import com.solum.xplain.calculation.value.CalculationPortfolioItemView;
import com.solum.xplain.calculation.value.CalculationResultForm;
import com.solum.xplain.calculation.value.CalculationResultTotalsForm;
import com.solum.xplain.calculation.value.CalculationResultView;
import com.solum.xplain.calculation.value.CalculationResultsTotals;
import com.solum.xplain.calculation.value.CashFlowsView;
import com.solum.xplain.calculation.value.DV01Combined;
import com.solum.xplain.calculation.value.Spot01Combined;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.value.AuditEntryItemView;
import com.solum.xplain.core.calculationapi.CalculationExportProvider;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.shared.utils.cache.CacheUnpaged;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
public class CalculationResultControllerService implements CalculationExportProvider {

  private final UserCalculationResultResolver userCalculationResultResolver;
  private final CalculationResultRepository calculationResultRepository;
  private final CalculationTotalsRepository calculationTotalsRepository;
  private final AuditEntryService auditEntryService;
  private final ApplicationEventPublisher eventPublisher;

  public CalculationResultControllerService(
      UserCalculationResultResolver userCalculationResultResolver,
      CalculationResultRepository calculationResultRepository,
      CalculationTotalsRepository calculationTotalsRepository,
      AuditEntryService auditEntryService,
      ApplicationEventPublisher eventPublisher) {
    this.userCalculationResultResolver = userCalculationResultResolver;
    this.calculationResultRepository = calculationResultRepository;
    this.calculationTotalsRepository = calculationTotalsRepository;
    this.auditEntryService = auditEntryService;
    this.eventPublisher = eventPublisher;
  }

  private static PortfolioCondensedView toPortfolioCondensedView(CalculationResultView result) {
    return PortfolioCondensedView.newOf(
        result.getPortfolioId(),
        result.getExternalPortfolioId(),
        result.getCompanyId(),
        result.getExternalCompanyId(),
        result.getEntityId(),
        result.getExternalEntityId());
  }

  public Either<ErrorItem, ScrollableEntry<CalculationResultView>> calculationList(
      Authentication authentication, ScrollRequest scrollRequest, TableFilter tableFilter) {
    return userCalculationResultResolver
        .entityTeamFilter(authentication)
        .map(f -> calculationResultRepository.listCalculations(f, tableFilter, scrollRequest));
  }

  public Either<ErrorItem, CalculationResultView> calculationResults(
      Authentication authentication, String id) {
    return getUserCalculationResult(authentication, id).map(UserCalculationResult::getView);
  }

  public Either<ErrorItem, EntityId> deleteResults(Authentication authentication, String id) {
    return userCalculationResultResolver
        .userCalculationResult(authentication, id, false)
        .flatMap(r -> calculationResultRepository.calculationEntity(id))
        .map(
            result -> {
              var deletedEvent = new CalculationDeletedEvent(result.getId());
              eventPublisher.publishEvent(deletedEvent);
              if (result.getCalculationResultStatus() == CalculationResultStatus.IN_PROGRESS) {
                var resultId = result.getId();
                var portfolioId = result.getPortfolioId().toHexString();
                var dashboardId = result.getDashboardId();
                var pnlExplainCalculationId = result.getPnlExplainCalculationId();
                var onboardingReportId = result.getOnboardingReportId();
                var canceledEvent =
                    CalculationCanceledEvent.newOf(
                        resultId,
                        portfolioId,
                        dashboardId,
                        pnlExplainCalculationId,
                        onboardingReportId);
                eventPublisher.publishEvent(canceledEvent);
              }
              return EntityId.entityId(result.getId());
            });
  }

  public Either<ErrorItem, EntityId> storeAsPermanent(
      Authentication authentication, String id, CalculationResultForm form) {
    return getUserCalculationResult(authentication, id)
        .flatMap(p -> calculationResultRepository.storeAsPermanent(p.getView().getId(), form));
  }

  public Either<ErrorItem, EntityId> updateCalculationResultsMetadata(
      Authentication authentication, String id, CalculationResultForm form) {
    return getUserCalculationResult(authentication, id)
        .flatMap(p -> calculationResultRepository.updateMetadata(p.getView().getId(), form));
  }

  public Either<ErrorItem, CalculationResultsTotals> calculationTotals(
      Authentication authentication,
      String id,
      TableFilter tableFilter,
      GroupRequest groupRequest,
      CalculationResultTotalsForm form) {
    return getUserCalculationResult(authentication, id)
        .flatMap(
            p ->
                calculationTotalsRepository.calculationResultTotals(
                    p.getView().getId(), tableFilter, groupRequest, form));
  }

  public Either<ErrorItem, CashFlowsView> calculationCashFlows(
      Authentication authentication,
      String id,
      TableFilter tableFilter,
      CalculationResultTotalsForm form) {
    return getUserCalculationResult(authentication, id)
        .map(p -> calculationTotalsRepository.cashFlows(p.getView().getId(), tableFilter, form));
  }

  public Either<ErrorItem, DV01Combined> calculationDV01Values(
      Authentication authentication,
      String id,
      TableFilter tableFilter,
      CalculationResultTotalsForm form) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationTotalsRepository.dv01TradeValues(
                    p.getView().getId(), tableFilter, form));
  }

  public Either<ErrorItem, Spot01Combined> calculationSpot01Values(
      Authentication authentication,
      String id,
      TableFilter tableFilter,
      CalculationResultTotalsForm form) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationTotalsRepository.spot01TradeValues(
                    p.getView().getId(), tableFilter, form));
  }

  @CacheUnpaged // No invalidation necessary, immutable.
  public Either<ErrorItem, ScrollableEntry<CalculationPortfolioItemView>>
      getCalculationPortfolioItems(
          Authentication authentication,
          String id,
          TableFilter tableFilter,
          ScrollRequest scrollRequest) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultRepository.calculationPortfolioItems(
                    p.getView().getId(), tableFilter, scrollRequest));
  }

  public Either<ErrorItem, ScrollableEntry<CalculationPortfolioItemView>>
      getAllCalculationPortfolioItems(
          String dashboardId, TableFilter tableFilter, ScrollRequest scrollRequest) {
    return calculationResultRepository.calculationPortfolioItemsByDashboardId(
        dashboardId, tableFilter, scrollRequest);
  }

  public Either<ErrorItem, ScrollableEntry<CalculationPortfolioItemView>>
      getCalculationPortfolioItems(
          Authentication authentication,
          String id,
          TableFilter tableFilter,
          ScrollRequest scrollRequest,
          GroupRequest groupRequest) {
    return getUserCalculationResult(authentication, id)
        .map(
            p ->
                calculationResultRepository.calculationPortfolioItems(
                    id, tableFilter, scrollRequest, groupRequest));
  }

  public Either<ErrorItem, FileResponseEntity> exportCalculationResultPortfolioItems(
      Authentication user, LocalDate stateDate, String id, Sort sort, TableFilter tableFilter) {
    return getUserCalculationResult(user, id)
        .map(
            p -> {
              var calcResultView = p.getView();
              var prefix =
                  String.format(
                      "%s_%s",
                      calcResultView.getValuationDate(), calcResultView.getExternalPortfolioId());
              var rows = toCsvRows(p, sort, tableFilter);
              return toFileResponseEntity(rows, prefix, stateDate);
            });
  }

  @Override
  public FileResponseEntity exportAllPVCalculationResults(
      List<String> calculationResultIds, LocalDate valuationDate, LocalDate stateDate) {
    var calculations = calculationResultRepository.calculationsByIds(calculationResultIds);
    var rows = toCsvRows(calculations);
    return toFileResponseEntity(rows, valuationDate.toString(), stateDate);
  }

  private List<CsvRow> toCsvRows(UserCalculationResult result, Sort sort, TableFilter tableFilter) {
    var mapper = PortfolioItemResultCsvMapper.mapper(result.getUserPortfolio().getView());
    var calcResultId = result.getView().getId();
    return calculationResultRepository
        .calculationPortfolioItemsForExportStream(calcResultId, sort, tableFilter)
        .map(mapper::toCsvRow)
        .toList();
  }

  private List<CsvRow> toCsvRows(List<CalculationResultView> results) {
    var portfolios =
        results.stream()
            .map(CalculationResultControllerService::toPortfolioCondensedView)
            .collect(toSet());
    var mapper = PortfolioItemResultCsvMapper.mapper(portfolios);
    var calcIds = results.stream().map(CalculationResultView::getId).toList();
    return calculationResultRepository
        .allCalculationPortfoliosItemsForExportStream(calcIds)
        .map(mapper::toCsvRow)
        .toList();
  }

  private FileResponseEntity toFileResponseEntity(
      List<CsvRow> rows, String prefix, LocalDate stateDate) {

    var csvFileName = resolveFilename(prefix, stateDate);
    var csvFile = new CsvOutputFile(CALCULATION_RESULTS_FIELDS, rows).writeToByteArray();
    return FileResponseEntity.csvFile(csvFile, csvFileName);
  }

  private String resolveFilename(String prefix, LocalDate stateDate) {
    var name = String.format("%s_PVCalculationResults", prefix);
    return nameWithTimeStamp(name, stateDate);
  }

  public Either<ErrorItem, ScrollableEntry<AuditEntryItemView>> getCalculationLogs(
      Authentication user,
      String id,
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      GroupRequest groupRequest) {
    return getUserCalculationResult(user, id)
        .flatMap(
            p ->
                auditEntryService
                    .entryByReference(
                        CalculationResult.CALCULATION_RESULT_COLLECTION, p.getView().getId())
                    .flatMap(
                        e ->
                            auditEntryService.logsView(
                                e.getId(), tableFilter, groupRequest, scrollRequest)))
        .orElse(() -> Either.right(ScrollableEntry.empty()));
  }

  private Either<ErrorItem, UserCalculationResult> getUserCalculationResult(
      Authentication auth, String calculationResultId) {
    return userCalculationResultResolver.userCalculationResult(auth, calculationResultId);
  }
}
