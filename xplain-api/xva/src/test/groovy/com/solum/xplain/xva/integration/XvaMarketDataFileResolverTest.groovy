package com.solum.xplain.xva.integration

import static com.solum.xplain.core.calibration.CurveSample.discountCurve
import static com.solum.xplain.core.calibration.CurveSample.indexCurve
import static com.solum.xplain.core.calibration.CurveSample.indexCurveUsd

import com.opengamma.strata.basics.ImmutableReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.calculation.CalculationResultMarketData
import com.solum.xplain.calculation.PortfolioCalculationData
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.calculation.value.CalculationOptions
import com.solum.xplain.calibration.rates.RatesCalibrationResult
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveFundingNode
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView
import com.solum.xplain.extensions.enums.CreditSeniority
import com.solum.xplain.xva.calculation.XvaMapper
import com.solum.xplain.xva.correlation.CorrelationMatrix
import com.solum.xplain.xva.correlation.CorrelationMatrixKey
import com.solum.xplain.xva.correlation.CorrelationMatrixValue
import com.solum.xplain.xva.correlation.value.CorrelationType
import com.solum.xplain.xva.correlation.value.CorrelationValuesMatrix
import com.solum.xplain.xva.proxy.messages.CorrelationsValue
import com.solum.xplain.xva.proxy.messages.CreditValue
import com.solum.xplain.xva.proxy.messages.DiscountFactorsValue
import com.solum.xplain.xva.proxy.messages.FundingSpreadsValue
import com.solum.xplain.xva.proxy.messages.FxSpotValue
import com.solum.xplain.xva.proxy.messages.SwaptionVolatilitiesValue
import com.solum.xplain.xva.settings.entity.XvaLiborIndex
import com.solum.xplain.xva.settings.entity.XvaLiborIndices
import com.solum.xplain.xva.settings.value.XvaSettingsView
import java.time.LocalDate
import spock.lang.Specification

class XvaMarketDataFileResolverTest extends Specification {
  //Calibration Data
  static RATES = ImmutableRatesProvider
  .builder(LocalDate.now())
  .discountCurve(Currency.EUR, discountCurve())
  .indexCurve(IborIndices.EUR_EURIBOR_1M, indexCurve())
  .build()
  static RATES_USD = ImmutableRatesProvider
  .builder(LocalDate.now())
  .discountCurve(Currency.USD, discountCurve())
  .indexCurve(IborIndices.USD_LIBOR_3M, indexCurveUsd())
  .build()
  static MARKET_DATA = ImmutableMarketData.builder(LocalDate.now()).build()

  // Curve Group Data
  static SURFACE = VolatilitySurfaceBuilder.surfaceWithNodes("EUR 1M Vols")
  static SURFACE_USD = VolatilitySurfaceBuilder.surfaceWithNodes("USD 3M Vols")
  static FX_VOLATILITY_NODE = new CurveGroupFxVolatilityNode()
  static FX_VOLATILITY = new CurveGroupFxVolatilityBuilder().nodes([FX_VOLATILITY_NODE] as Set).build()

  //Expected data
  static FX_SPOT_VALUE = new FxSpotValue([], [])
  static CORRELATION_VALUE = new CorrelationsValue([], [], new Double[][]{})
  static DISCOUNT_FACTORS_USD_FLOATING = new DiscountFactorsValue([LocalDate.now()], [3.0d], CalculationType.IBOR.xvaLabel)
  static DISCOUNT_FACTORS_USD_FUNDING = new DiscountFactorsValue([LocalDate.now()], [1.0d],null)
  static DISCOUNT_FACTORS_EUR_FLOATING = new DiscountFactorsValue([LocalDate.now()], [2.0d],CalculationType.IBOR.xvaLabel)
  static DISCOUNT_FACTORS_EUR_FUNDING = new DiscountFactorsValue([LocalDate.now()], [1.0d],null)
  static SWAPTION_VOLS_VALUE = new SwaptionVolatilitiesValue(1, 2, "fixedDCT", "floatDCT", ["X"], ["Y"], new double[][]{})
  static FUNDING_SPREAD_VALUE = new FundingSpreadsValue(["SELF", "CORP"], [0.0d, 1d])
  static CREDIT_VALUE = new CreditValue("CORP", 0.1d, ["X"], [2.0d])
  static SELF_CREDIT_VALUE = new CreditValue("SELF", 0.1d, ["X"], [2.0d])

  def xvaMapper = Mock(XvaMapper)
  def resolver = new XvaMarketDataFileResolver(xvaMapper)

  def "should resolve XvaMarketDataFile"() {
    setup:
    def selfCreditCurve = creditCurveNoFunding("EUR", CreditSeniority.SNRFOR, "SELF")
    def creditCurve = creditCurve("EUR", CreditSeniority.SNRFOR)
    def portfolioCalculationData = calculationDataEurDiscounting(reportingCurrency, [selfCreditCurve, creditCurve])
    def xvaSettings = new XvaSettingsView(
    sigmaStep: 1.0d,
    hullWhiteMeanReversion: 2.0d,
    timeEnd: 3.0d,
    )
    def liborIndices = new XvaLiborIndices(
    liborIndices: [new XvaLiborIndex(currency: "EUR", index: "EUR-EURIBOR-1M")]
    )
    def correlationMatrix = Mock(CorrelationMatrix)
    1 * correlationMatrix.filterValues(["EUR", "USD"] as Set<String>, "EUR") >> correlationMatrix
    1 * correlationMatrix.getValues() >> [
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "USD", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "USD", CorrelationType.FX, 1.0),
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "GBP", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "GBP", CorrelationType.FX, 1.0),
      CorrelationMatrixValue.of("GBP", CorrelationType.IR, "USD", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("GBP", CorrelationType.IR, "USD", CorrelationType.FX, 1.0),
    ]

    def expectedMatrixRows = [
      CorrelationMatrixKey.of("EUR", CorrelationType.IR),
      CorrelationMatrixKey.of("GBP", CorrelationType.IR),
      CorrelationMatrixKey.of("USD", CorrelationType.IR),
      CorrelationMatrixKey.of("GBP", CorrelationType.FX),
      CorrelationMatrixKey.of("USD", CorrelationType.FX),
    ]

    1 * xvaMapper.fxSpotOf(MARKET_DATA, Currency.EUR) >> FX_SPOT_VALUE
    1 * xvaMapper.correlationsOf({
      it -> {
        it instanceof CorrelationValuesMatrix &&
        ((CorrelationValuesMatrix) it).getRows() == expectedMatrixRows &&
        ((CorrelationValuesMatrix) it).getColumns() == expectedMatrixRows
      }
    }) >> CORRELATION_VALUE

    1 * xvaMapper.fxVol([FX_VOLATILITY_NODE], MARKET_DATA) >> ["EUR/GBP": ["1Y": new FxVolatilityNodeValueView(value: 2.0d)]]

    1 * xvaMapper.ofFunding(Currency.EUR, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_EUR_FUNDING
    1 * xvaMapper.ofIndex(IborIndices.EUR_EURIBOR_1M, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_EUR_FLOATING

    1 * xvaMapper.ofFunding(Currency.USD, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_USD_FUNDING
    1 * xvaMapper.swaptionVols(SURFACE, MARKET_DATA) >> SWAPTION_VOLS_VALUE

    1 * xvaMapper.fundingSpreadsOf([selfCreditCurve, creditCurve], MARKET_DATA) >> FUNDING_SPREAD_VALUE
    1 * xvaMapper.creditValueOf(selfCreditCurve, MARKET_DATA) >> SELF_CREDIT_VALUE
    1 * xvaMapper.creditValueOf(creditCurve, MARKET_DATA) >> CREDIT_VALUE
    when:
    def result = resolver.calculationMarketFile(xvaSettings, liborIndices, portfolioCalculationData, correlationMatrix)

    then:
    result.isRight()
    with(result.getOrNull()) {
      get("Numeraire") == "EUR"
      get("CollateralCcy") == "EUR"
      get("AnchorDate") == LocalDate.now()
      get("Currencies") == ["EUR", "USD"]
      get("Credits") == ["CORP", "SELF"] as Set
      get("SigmaStep") == 1
      get("HWRevert") == 2.0
      get("TStar") == 3.0
      def fxSpot = (FxSpotValue) get("FxSpot")
      fxSpot.symbol == this.FX_SPOT_VALUE.symbol
      fxSpot.rate == this.FX_SPOT_VALUE.rate
      get("Correlations_EUR") == this.CORRELATION_VALUE
      get("DiscountFactorsFunding_USD") == this.DISCOUNT_FACTORS_USD_FUNDING

      //This is where we cannot resolve the index curve and use the funding of the numeraire
      get("DiscountFactorsFloating_USD") == this.DISCOUNT_FACTORS_EUR_FUNDING
      get("DiscountFactorsFunding_EUR") == this.DISCOUNT_FACTORS_EUR_FUNDING
      get("DiscountFactorsFloating_EUR") == this.DISCOUNT_FACTORS_EUR_FLOATING
      get("SwaptionVols_EUR") == this.SWAPTION_VOLS_VALUE
      get("FundingSpreads") == this.FUNDING_SPREAD_VALUE
      get("Credit_CORP") == this.CREDIT_VALUE
      get("Credit_SELF") == this.SELF_CREDIT_VALUE
      // Verify IR basis spreads are present for all currencies
      get("IRBasisSpreads_EUR") != null
      get("IRBasisSpreads_USD") != null
    }

    where:
    reportingCurrency << [Currency.EUR, Currency.USD]
  }

  def "should resolve XvaMarketDataFile with USD numeraire"() {
    setup:
    def selfCreditCurve = creditCurveNoFunding("USD", CreditSeniority.SNRFOR, "SELF")
    def creditCurve = creditCurve("USD", CreditSeniority.SNRFOR)
    def portfolioCalculationData = calculationDataUsdDiscounting(reportingCurrency, [selfCreditCurve, creditCurve])
    def xvaSettings = new XvaSettingsView(
    sigmaStep: 1.0d,
    hullWhiteMeanReversion: 2.0d,
    timeEnd: 3.0d,
    )
    def liborIndices = new XvaLiborIndices(
    liborIndices: [new XvaLiborIndex(currency: "USD", index: "USD-LIBOR-3M")]
    )
    def correlationMatrix = Mock(CorrelationMatrix)
    1 * correlationMatrix.filterValues(["EUR", "USD"] as Set<String>, "USD") >> correlationMatrix
    1 * correlationMatrix.getValues() >> [
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "USD", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "GBP", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("EUR", CorrelationType.IR, "GBP", CorrelationType.FX, 1.0),
      CorrelationMatrixValue.of("GBP", CorrelationType.IR, "USD", CorrelationType.IR, 1.0),
      CorrelationMatrixValue.of("GBP", CorrelationType.IR, "EUR", CorrelationType.FX, 1.0),
    ]

    def expectedMatrixRows = [
      CorrelationMatrixKey.of("USD", CorrelationType.IR),
      CorrelationMatrixKey.of("EUR", CorrelationType.IR),
      CorrelationMatrixKey.of("GBP", CorrelationType.IR),
      CorrelationMatrixKey.of("EUR", CorrelationType.FX),
      CorrelationMatrixKey.of("GBP", CorrelationType.FX),
    ]

    1 * xvaMapper.fxSpotOf(MARKET_DATA, Currency.USD) >> FX_SPOT_VALUE
    1 * xvaMapper.correlationsOf({
      it -> {
        it instanceof CorrelationValuesMatrix &&
        ((CorrelationValuesMatrix) it).getRows() == expectedMatrixRows &&
        ((CorrelationValuesMatrix) it).getColumns() == expectedMatrixRows
      }
    }) >> CORRELATION_VALUE

    1 * xvaMapper.fxVol([FX_VOLATILITY_NODE], MARKET_DATA) >> ["EUR/GBP": ["1Y": new FxVolatilityNodeValueView(value: 2.0d)]]

    1 * xvaMapper.ofIndex(IborIndices.USD_LIBOR_3M, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_USD_FLOATING

    1 * xvaMapper.ofFunding(Currency.EUR, _ as ImmutableRatesProvider, _ as LocalDate) >> null

    2 * xvaMapper.ofFunding(Currency.USD, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_USD_FUNDING

    1 * xvaMapper.swaptionVols(SURFACE_USD, MARKET_DATA) >> SWAPTION_VOLS_VALUE

    1 * xvaMapper.fundingSpreadsOf([selfCreditCurve, creditCurve], MARKET_DATA) >> FUNDING_SPREAD_VALUE
    1 * xvaMapper.creditValueOf(selfCreditCurve, MARKET_DATA) >> SELF_CREDIT_VALUE
    1 * xvaMapper.creditValueOf(creditCurve, MARKET_DATA) >> CREDIT_VALUE
    when:
    def result = resolver.calculationMarketFile(xvaSettings, liborIndices, portfolioCalculationData, correlationMatrix)

    then:
    result.isRight()
    with(result.getOrNull()) {
      get("Numeraire") == "USD"
      get("CollateralCcy") == "USD"
      get("AnchorDate") == LocalDate.now()
      get("Currencies") == ["USD", "EUR"]
      get("Credits") == ["CORP", "SELF"] as Set
      get("SigmaStep") == 1
      get("HWRevert") == 2.0
      get("TStar") == 3.0
      def fxSpot = (FxSpotValue) get("FxSpot")
      fxSpot.symbol == this.FX_SPOT_VALUE.symbol
      fxSpot.rate == this.FX_SPOT_VALUE.rate
      get("Correlations_USD") == this.CORRELATION_VALUE
      get("DiscountFactorsFunding_USD") == this.DISCOUNT_FACTORS_USD_FUNDING
      get("DiscountFactorsFloating_USD") == this.DISCOUNT_FACTORS_USD_FLOATING
      get("DiscountFactorsFunding_EUR") == this.DISCOUNT_FACTORS_USD_FUNDING
      get("DiscountFactorsFloating_EUR") == this.DISCOUNT_FACTORS_USD_FUNDING
      get("Credit_CORP") == this.CREDIT_VALUE
      get("Credit_SELF") == this.SELF_CREDIT_VALUE
    }

    where:
    reportingCurrency << [Currency.EUR, Currency.USD]
  }

  def "should fail to resolve XvaMarketDataFile with invalid credit curves"() {
    setup:
    def portfolioCalculationData = calculationDataUsdDiscounting(Currency.USD, creditCurves)
    def xvaSettings = new XvaSettingsView()
    def liborIndices = new XvaLiborIndices()
    def correlationMatrix = new CorrelationMatrix()

    when:
    def result = resolver.calculationMarketFile(xvaSettings, liborIndices, portfolioCalculationData, correlationMatrix)

    then:
    result.isLeft()
    result.left().get() == Error.CALCULATION_ERROR.entity(errorMessage)

    where:
    creditCurves                                                                                                       | errorMessage
    [creditCurve("EUR", CreditSeniority.SNRFOR), creditCurveNoFunding("EUR", CreditSeniority.SNRFOR, "SELF")]          | "No valid credit curve exists for discount ccy USD"
    [creditCurve("USD", CreditSeniority.SECDOM), creditCurveNoFunding("USD", CreditSeniority.SECDOM, "SELF")]          | "No credit curve with seniority SNRFOR and discount ccy USD exists"
    [creditCurve("USD", CreditSeniority.SNRFOR)]                                                                       | "Missing SELF credit curve with seniority SNRFOR and discount ccy USD"
    [creditCurve("USD", CreditSeniority.SNRFOR), creditCurve("USD", CreditSeniority.SECDOM, "SELF")]                   | "Missing SELF credit curve with seniority SNRFOR and discount ccy USD"
    [creditCurveNoFunding("USD", CreditSeniority.SNRFOR), creditCurveNoFunding("USD", CreditSeniority.SNRFOR, "SELF")] | "Credit curves with following corp tickers have missing funding nodes: CORP"
    [creditCurveNoFunding("USD", CreditSeniority.SNRFOR), creditCurve("USD", CreditSeniority.SNRFOR, "SELF")]          | "Credit curves with following corp tickers have missing funding nodes: CORP"
    [creditCurve("USD", CreditSeniority.SNRFOR), creditCurveNoFunding("USD", CreditSeniority.SNRFOR, "CORP2"),
      creditCurve("USD", CreditSeniority.SNRFOR, "SELF")]                                                               | "Credit curves with following corp tickers have missing funding nodes: CORP2"
  }

  def "should fail to resolve XvaMarketDataFile with invalid volatility surfaces"() {
    setup:
    def selfCreditCurve = creditCurveNoFunding("USD", CreditSeniority.SNRFOR, "SELF")
    def creditCurve = creditCurve("USD", CreditSeniority.SNRFOR)
    def portfolioCalculationData = calculationDataUsdDiscounting(Currency.USD, [selfCreditCurve, creditCurve], volatilitySurfaces)
    def xvaSettings = new XvaSettingsView()
    def liborIndices = new XvaLiborIndices(
    liborIndices: [new XvaLiborIndex(currency: "USD", index: projectionIndex)]
    )
    def correlationMatrix = new CorrelationMatrix()

    1 * xvaMapper.fxSpotOf(_, _) >> FX_SPOT_VALUE
    1 * xvaMapper.correlationsOf(_) >> CORRELATION_VALUE
    1 * xvaMapper.fxVol(_, _) >> ["EUR/GBP": ["1Y": new FxVolatilityNodeValueView(value: 2.0d)]]
    numIndexCalls * xvaMapper.ofIndex(_, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_USD_FLOATING
    1 * xvaMapper.ofFunding(Currency.EUR, _ as ImmutableRatesProvider, _ as LocalDate) >> null
    2 * xvaMapper.ofFunding(Currency.USD, _ as ImmutableRatesProvider, _ as LocalDate) >> DISCOUNT_FACTORS_USD_FUNDING

    when:
    def result = resolver.calculationMarketFile(xvaSettings, liborIndices, portfolioCalculationData, correlationMatrix)

    then:
    result.isLeft()
    result.left().get() == Error.CALCULATION_ERROR.entity(errorMessage)

    where:
    volatilitySurfaces                                            | numIndexCalls | projectionIndex | errorMessage
    []                                                            | 1             | "USD-LIBOR-3M"  | "No volatility surfaces with nodes defined"
    [new VolatilitySurfaceBuilder().name("USD-LIBOR-3M").build()] | 1             | "USD-LIBOR-3M"  | "No volatility surfaces with nodes defined"
    [SURFACE_USD]                                                 | 0             | "USD-LIBOR-1M"  | "Could not find volatility surfaces mapped to indexes defined in XVA main projection index settings"
    [SURFACE_USD]                                                 | 0             | "USD-LIBOR-6M"  | "Could not find volatility surfaces mapped to indexes defined in XVA main projection index settings"
  }

  PortfolioCalculationData calculationDataEurDiscounting(Currency reportingCurrency = Currency.USD, List<CreditCurve> creditCurves) {
    PortfolioCalculationData.builder()
    .portfolio(new PortfolioCondensedView())
    .options(CalculationOptions.newOf(
    LocalDate.now(),
    LocalDate.now().plusDays(1),
    BitemporalDate.newOf(LocalDate.ofEpochDay(0)),
    reportingCurrency,
    PortfolioCalculationType.XVA,
    new CalculationDiscounting(CalculationDiscountingType.DISCOUNT_EUR, CalculationStrippingType.OIS, Currency.USD, false),
    InstrumentPriceRequirements.bidRequirements()))
    .marketData(new CalculationResultMarketData())
    .referenceData(ImmutableReferenceData.empty())
    .calibrationResult(
    CalculationCurveGroupData.builder()
    .creditCurves(creditCurves)
    .fxVolatility(FX_VOLATILITY)
    .marketData(MARKET_DATA)
    .rates(["EUR": new RatesCalibrationResult(RATES, [])])
    .volatilitySurfaces([SURFACE])
    .build())
    .calculationTrades(Mock(CalculationTrades))
    .build()
  }

  PortfolioCalculationData calculationDataUsdDiscounting(Currency reportingCurrency = Currency.USD, List<CreditCurve> creditCurves, List<VolatilitySurface> volatilitySurfaces = [SURFACE_USD]) {
    PortfolioCalculationData.builder()
    .portfolio(new PortfolioCondensedView())
    .options(CalculationOptions.newOf(
    LocalDate.now(),
    LocalDate.now().plusDays(1),
    BitemporalDate.newOf(LocalDate.ofEpochDay(0)),
    reportingCurrency,
    PortfolioCalculationType.XVA,
    new CalculationDiscounting(CalculationDiscountingType.DISCOUNT_USD, CalculationStrippingType.OIS, Currency.USD, false),
    InstrumentPriceRequirements.bidRequirements()))
    .marketData(new CalculationResultMarketData())
    .referenceData(ImmutableReferenceData.empty())
    .calibrationResult(
    CalculationCurveGroupData.builder()
    .creditCurves(creditCurves)
    .fxVolatility(FX_VOLATILITY)
    .marketData(MARKET_DATA)
    .rates(["USD": new RatesCalibrationResult(RATES_USD, [])])
    .volatilitySurfaces(volatilitySurfaces)
    .build())
    .calculationTrades(Mock(CalculationTrades))
    .build()
  }

  def creditCurve(String currency, CreditSeniority seniority, String corpTicker = "CORP") {
    new CreditCurve(
    corpTicker: corpTicker,
    name: "LONG NAME",
    currency: currency,
    seniority: seniority,
    fundingNodes: [new CreditCurveFundingNode(tenor: "1Y")],
    )
  }

  def creditCurveNoFunding(String currency, CreditSeniority seniority, String corpTicker = "CORP") {
    new CreditCurve(
    corpTicker: corpTicker,
    name: "LONG NAME",
    currency: currency,
    seniority: seniority,
    )
  }
}
