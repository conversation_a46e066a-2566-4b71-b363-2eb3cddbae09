package com.solum.xplain.xva.integration;

import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.solum.xplain.core.error.Error.CALCULATION_ERROR;
import static com.solum.xplain.core.utils.RatesUtils.combine;
import static com.solum.xplain.xva.correlation.CorrelationMatrixMapper.CORRELATION_MATRIX_MAPPER;
import static java.lang.String.format;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableSet;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider;
import com.solum.xplain.calculation.PortfolioCalculationData;
import com.solum.xplain.calculation.curvegroup.CalculationCurveGroupData;
import com.solum.xplain.calibration.rates.RatesCalibrationResult;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.enums.CreditSeniority;
import com.solum.xplain.xva.calculation.XvaMapper;
import com.solum.xplain.xva.calculation.constants.CalculationPerspective;
import com.solum.xplain.xva.correlation.CorrelationMatrix;
import com.solum.xplain.xva.proxy.messages.CorrelationsValue;
import com.solum.xplain.xva.proxy.messages.DiscountFactorsValue;
import com.solum.xplain.xva.proxy.messages.FxVolValue;
import com.solum.xplain.xva.proxy.messages.XvaMarketFile;
import com.solum.xplain.xva.proxy.messages.XvaMarketFileBuilder;
import com.solum.xplain.xva.settings.entity.XvaLiborIndex;
import com.solum.xplain.xva.settings.entity.XvaLiborIndices;
import com.solum.xplain.xva.settings.value.XvaSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class XvaMarketDataFileResolver {
  private static final int CALIBRATED_VALUES_PERIOD_YEARS = 50;

  private final XvaMapper xvaMapper;

  public XvaMarketDataFileResolver(XvaMapper xvaMapper) {
    this.xvaMapper = xvaMapper;
  }

  public Either<ErrorItem, XvaMarketFile> calculationMarketFile(
      XvaSettingsView xvaSettings,
      XvaLiborIndices liborIndices,
      PortfolioCalculationData data,
      CorrelationMatrix correlationMatrix) {
    try {
      var curveGroupData = data.getCalibrationResult();
      var calculationMarketData = data.getCalibrationResult().getMarketData();
      var calibrationResult = data.getCalibrationResult();
      var options = data.getOptions();
      var discountingCcy = options.getDiscounting().getDiscountCurrency();
      if (discountingCcy == null) {
        throw new IllegalArgumentException("Discounting ccy can not be null");
      }

      var ratesProvider =
          data.getCalibrationResult().getRates().values().stream()
              .map(RatesCalibrationResult::getRatesProvider)
              .collect(collectingAndThen(toList(), l -> combine(calculationMarketData, l)));

      var creditCurvesEither = filterAndValidateCreditCurves(data, discountingCcy);
      if (creditCurvesEither.isLeft()) {
        return Either.left(creditCurvesEither.left().get());
      }

      var creditCurves = creditCurvesEither.right().get();
      var currencies = resolveCurrencies(data, ratesProvider);

      XvaMarketFileBuilder builder =
          new XvaMarketFileBuilder(discountingCcy.getCode())
              .anchorDate(options.getValuationDate())
              .currencies(currencies.stream().map(Currency::getCode).toList())
              .credits(
                  creditCurves.stream()
                      .map(CreditCurve::getCorpTicker)
                      .collect(toUnmodifiableSet()))
              .settings(xvaSettings.getSigmaStep(), xvaSettings.getHullWhiteMeanReversion())
              .tstar(xvaSettings.getTimeEnd())
              .fxSpot(xvaMapper.fxSpotOf(calculationMarketData, discountingCcy))
              .correlations(correlationsValue(correlationMatrix, currencies, discountingCcy));
      var fxVolatilityNodes =
          Optional.of(calibrationResult)
              .map(CalculationCurveGroupData::getFxVolatility)
              .map(CurveGroupFxVolatility::getNodes)
              .map(List::copyOf)
              .orElse(List.of());
      xvaMapper
          .fxVol(fxVolatilityNodes, calculationMarketData)
          .forEach(
              (k, v) ->
                  builder.fxVolWithSlash(
                      k,
                      new FxVolValue(
                          ImmutableList.copyOf(v.keySet()),
                          v.values().stream().map(FxVolatilityNodeValueView::getValue).toList())));

      resolveXvaDiscountFactors(builder, data, liborIndices, ratesProvider, discountingCcy);

      var applicableVolatilitySurfaces =
          filterAndValidateVolatilitySurfaces(curveGroupData, liborIndices);

      if (applicableVolatilitySurfaces.isLeft()) {
        return Either.left(applicableVolatilitySurfaces.left().get());
      }

      applicableVolatilitySurfaces
          .right()
          .get()
          .forEach(
              v ->
                  builder.swaptionVols(
                      v.applicableRateIndex().getCurrency().getCode(),
                      xvaMapper.swaptionVols(v, calculationMarketData)));

      builder.fundingSpreads(xvaMapper.fundingSpreadsOf(creditCurves, calculationMarketData));
      creditCurves.stream()
          .map(c -> xvaMapper.creditValueOf(c, calculationMarketData))
          .forEach(builder::creditValue);

      // Add IR basis spreads for all currencies (required for XVA 4.0 validation)
      currencies.forEach(currency ->
          builder.irBasisSpreads(currency.getCode(), xvaMapper.irBasisSpreadsOf(currency)));

      return Either.right(builder.build());
    } catch (IllegalArgumentException ex) {
      return Either.left(new ErrorItem(CALCULATION_ERROR, ex));
    }
  }

  private Either<ErrorItem, List<VolatilitySurface>> filterAndValidateVolatilitySurfaces(
      CalculationCurveGroupData curveGroupData, XvaLiborIndices liborIndices) {
    List<VolatilitySurface> applicableVolatilitySurfaces =
        curveGroupData.getVolatilitySurfaces().stream()
            .filter(v -> !CollectionUtils.isEmpty(v.getNodes()))
            .toList();

    if (applicableVolatilitySurfaces.isEmpty()) {
      return Either.left(
          new ErrorItem(CALCULATION_ERROR, "No volatility surfaces with nodes defined"));
    }

    applicableVolatilitySurfaces =
        applicableVolatilitySurfaces.stream()
            .filter(
                v ->
                    liborIndices.getLiborIndices().stream()
                        .anyMatch(i -> i.getIndex().equals(v.applicableRateIndex().getName())))
            .toList();

    if (applicableVolatilitySurfaces.isEmpty()) {
      return Either.left(
          new ErrorItem(
              CALCULATION_ERROR,
              "Could not find volatility surfaces mapped to indexes defined in XVA main projection index settings"));
    }

    return Either.right(applicableVolatilitySurfaces);
  }

  private Either<ErrorItem, List<CreditCurve>> filterAndValidateCreditCurves(
      PortfolioCalculationData data, Currency discountingCcy) {

    var creditCurves =
        creditCurves(data).stream()
            .filter(c -> c.getCorpTicker() != null)
            .filter(c -> c.getCurrency().equals(discountingCcy.getCode()))
            .toList();

    if (creditCurves.isEmpty()) {
      return Either.left(
          new ErrorItem(
              CALCULATION_ERROR,
              format(
                  "No valid credit curve exists for discount ccy %s", discountingCcy.getCode())));
    }

    creditCurves =
        creditCurves.stream().filter(c -> c.getSeniority() == CreditSeniority.SNRFOR).toList();

    if (creditCurves.isEmpty()) {
      return Either.left(
          new ErrorItem(
              CALCULATION_ERROR,
              format(
                  "No credit curve with seniority %s and discount ccy %s exists",
                  CreditSeniority.SNRFOR, discountingCcy.getCode())));
    }

    Predicate<CreditCurve> selfCreditCurvePredicate =
        (CreditCurve c) ->
            c.getCorpTicker().equalsIgnoreCase(CalculationPerspective.SELF.xvaValue());

    if (creditCurves.stream()
        .noneMatch(
            c -> selfCreditCurvePredicate.test(c) && c.getSeniority() == CreditSeniority.SNRFOR)) {
      return Either.left(
          new ErrorItem(
              CALCULATION_ERROR,
              format(
                  "Missing SELF credit curve with seniority %s and discount ccy %s",
                  CreditSeniority.SNRFOR, discountingCcy.getCode())));
    }

    // non-SELF credit curves should all have funding nodes
    var missingFundingNodeCurves =
        creditCurves.stream()
            .filter(c -> !selfCreditCurvePredicate.test(c))
            .filter(c -> c.getFundingNodes().isEmpty())
            .toList();

    if (!missingFundingNodeCurves.isEmpty()) {
      return Either.left(
          new ErrorItem(
              CALCULATION_ERROR,
              format(
                  "Credit curves with following corp tickers have missing funding nodes: %s",
                  missingFundingNodeCurves.stream()
                      .map(CreditCurve::getCorpTicker)
                      .collect(Collectors.joining(",")))));
    }

    return Either.right(creditCurves);
  }

  private CorrelationsValue correlationsValue(
      CorrelationMatrix matrix, Set<Currency> currencies, Currency discountCurrency) {
    var filtered =
        matrix.filterValues(correlationCurrencies(currencies), discountCurrency.getCode());
    return xvaMapper.correlationsOf(
        CORRELATION_MATRIX_MAPPER
            .convertWithDiscountCurrency(filtered, discountCurrency)
            .getValues());
  }

  private Set<String> correlationCurrencies(Set<Currency> currencies) {
    var builder = ImmutableSet.<String>builder();
    builder.add(Currency.EUR.getCode());
    if (currencies.size() == 1 && currencies.contains(Currency.EUR)) {
      builder.add(Currency.USD.getCode());
    }
    currencies.stream().map(Currency::getCode).forEach(builder::add);
    return builder.build();
  }

  private void resolveXvaDiscountFactors(
      XvaMarketFileBuilder builder,
      PortfolioCalculationData data,
      XvaLiborIndices liborIndices,
      ImmutableRatesProvider rates,
      Currency discountingCcy) {
    LocalDate endDate = data.valuationDate().plusYears(CALIBRATED_VALUES_PERIOD_YEARS);

    Map<Currency, IborIndex> xvaIndices =
        liborIndices.getLiborIndices().stream()
            .filter(XvaLiborIndex::isValid)
            .filter(i -> rates.getIndexCurves().containsKey(i.index()))
            .collect(toImmutableMap(XvaLiborIndex::currency, XvaLiborIndex::index, (a, b) -> a));
    var currencies = resolveCurrencies(data, rates);
    for (Currency c : currencies) {
      DiscountFactorsValue fundingDiscountFactors = xvaMapper.ofFunding(c, rates, endDate);

      // if we cannot resolve discount factors for a counter currency, we use the discount factors
      // from the index curve in the numeraire (discounting) ccy => e.g., if numeraire is EUR, and
      // we do not have USD discount factors discountFactors_USD is set equal to discountFactors_EUR
      if (c != discountingCcy && fundingDiscountFactors == null) {
        fundingDiscountFactors = xvaMapper.ofFunding(discountingCcy, rates, endDate);
      }

      builder.discountFactorsFunding(c.getCode(), fundingDiscountFactors);
      if (xvaIndices.containsKey(c)) {
        builder.discountFactorsFloating(
            c.getCode(), xvaMapper.ofIndex(xvaIndices.get(c), rates, endDate));
      } else {
        builder.discountFactorsFloating(c.getCode(), fundingDiscountFactors);
      }
    }
  }

  private List<CreditCurve> creditCurves(PortfolioCalculationData data) {
    return data.getCalibrationResult().getCreditCurves();
  }

  private Set<Currency> resolveCurrencies(
      PortfolioCalculationData data, ImmutableRatesProvider ratesProvider) {

    LinkedHashSet<Currency> currenciesSet = new LinkedHashSet<>();
    Optional.ofNullable(data.discounting().getDiscountCurrency()).ifPresent(currenciesSet::add);
    currenciesSet.addAll(ratesProvider.getDiscountCurrencies());
    currenciesSet.add(data.reportingCcy());

    // always add EUR to the set of currencies if only one currency is resolved (unless it is EUR,
    // then add USD)
    if (currenciesSet.size() == 1) {
      if (currenciesSet.contains(Currency.EUR)) {
        currenciesSet.add(Currency.USD);
      } else {
        currenciesSet.add(Currency.EUR);
      }
    }

    return ImmutableSet.copyOf(currenciesSet);
  }
}
