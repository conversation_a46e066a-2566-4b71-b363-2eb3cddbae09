package com.solum.xplain.xva.calculation;

import static com.google.common.collect.ImmutableList.toImmutableList;
import static com.google.common.collect.Tables.transpose;
import static com.solum.xplain.core.curvegroup.volatility.VolatilityMapper.toNodeValueViewForXva;
import static com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixValues.configurationFromList;
import static com.solum.xplain.xva.calculation.constants.XvaDayCounts.toXvaDayCount;
import static java.lang.Math.toIntExact;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static org.springframework.util.Assert.notNull;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableTable;
import com.google.common.collect.Table;
import com.google.common.collect.Tables;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.schedule.SchedulePeriod;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.data.FxRateId;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.pricer.DiscountFactors;
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.swap.type.FloatRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention;
import com.opengamma.strata.product.swap.type.OvernightRateSwapLegConvention;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNode;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceNode;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilityNodeValueView;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.xva.correlation.CorrelationMatrixKey;
import com.solum.xplain.xva.correlation.value.CorrelationValuesMatrix;
import com.solum.xplain.xva.proxy.messages.CorrelationsValue;
import com.solum.xplain.xva.proxy.messages.CreditValue;
import com.solum.xplain.xva.proxy.messages.DiscountFactorsValue;
import com.solum.xplain.xva.proxy.messages.FundingSpreadsValue;
import com.solum.xplain.xva.proxy.messages.FxSpotValue;
import com.solum.xplain.xva.proxy.messages.IRBasisSpreadsValue;
import com.solum.xplain.xva.proxy.messages.SwaptionVolatilitiesValue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class XvaMapper {
  private final ReferenceData referenceData;
  private final CurveGroupFxVolatilityMapper curveGroupFxVolatilityMapper;

  private static final String DEFAULT_CDS_FUNDING_TENOR = "5Y";

  public FxSpotValue fxSpotOf(MarketData marketData, Currency baseCcy) {
    Map<CurrencyPair, Double> collect =
        marketData.getIds().stream()
            .filter(FxRateId.class::isInstance)
            .map(FxRateId.class::cast)
            .collect(
                Collectors.toMap(
                    FxRateId::getPair, v -> marketData.getValue(v).fxRate(v.getPair())));

    Table<CurrencyPair, String, Double> table =
        Stream.concat(collect.entrySet().stream(), baseCcySelfRate(baseCcy).entrySet().stream())
            .map(e -> convertForBaseCcy(baseCcy, e))
            .collect(
                ImmutableTable.toImmutableTable(
                    Map.Entry::getKey, v -> "value", Map.Entry::getValue));
    Table<String, CurrencyPair, Double> transpose = Tables.transpose(table);
    Map<CurrencyPair, Double> values = transpose.row("value");
    List<String> symbol =
        values.keySet().stream()
            .map(p -> p.getBase().getCode() + p.getCounter().getCode())
            .toList();
    List<Double> rate = ImmutableList.copyOf(values.values());
    return new FxSpotValue(symbol, rate);
  }

  private static Map.Entry<CurrencyPair, Double> convertForBaseCcy(
      Currency baseCcy, Map.Entry<CurrencyPair, Double> e) {
    if (!baseCcy.equals(e.getKey().getCounter()) && baseCcy.equals(e.getKey().getBase())) {
      return Map.entry(
          CurrencyPair.of(e.getKey().getCounter(), e.getKey().getBase()), 1 / e.getValue());
    } else {
      return e;
    }
  }

  public Map<String, Map<String, FxVolatilityNodeValueView>> fxVol(
      List<CurveGroupFxVolatilityNode> fxVolatilities, MarketData marketData) {
    var fxVolatilityMatrixCondensedValues =
        fxVolatilities.stream()
            .map(node -> toView(marketData, node))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(collectingAndThen(toList(), FxVolatilityMatrixValues::configurationFromList));

    return transpose(fxVolatilityMatrixCondensedValues.getTable()).rowMap();
  }

  private Optional<FxVolatilityNodeValueView> toView(
      MarketData marketData, CurveGroupFxVolatilityNode node) {
    return marketData
        .findValue(MarketDataUtils.quoteId(node.getKey()))
        .map(
            marketValue ->
                curveGroupFxVolatilityMapper.toFxNodeValueView(
                    node, marketValue, marketData.getValuationDate()));
  }

  public CorrelationsValue correlationsOf(CorrelationValuesMatrix matrix) {
    notNull(matrix, "Correlation values matrix cannot be empty");
    ImmutableList<String> rows =
        matrix.getRows().stream().map(CorrelationMatrixKey::toString).collect(toImmutableList());
    ImmutableList<String> columns =
        matrix.getColumns().stream().map(CorrelationMatrixKey::toString).collect(toImmutableList());
    return new CorrelationsValue(rows, columns, matrix.getValues());
  }

  public FundingSpreadsValue fundingSpreadsOf(List<CreditCurve> curves, MarketData marketData) {
    ImmutableList.Builder<String> name = ImmutableList.builder();
    ImmutableList.Builder<Double> spread = ImmutableList.builder();
    name.addAll(curves.stream().map(CreditCurve::getCorpTicker).collect(toImmutableList()));
    spread.addAll(
        curves.stream()
            .map(
                v ->
                    v.fundingNodeByTenorOrFirst(DEFAULT_CDS_FUNDING_TENOR)
                        .map(n -> n.getKey(v.getReference(), v.getCurrency()))
                        .map(MarketDataUtils::quoteId)
                        .flatMap(marketData::findValue)
                        .orElse(BigDecimal.ZERO.doubleValue()))
            .collect(toImmutableList()));
    return new FundingSpreadsValue(name.build(), spread.build());
  }

  public SwaptionVolatilitiesValue swaptionVols(VolatilitySurface surface, MarketData marketData) {
    List<VolatilitySurfaceNode> nodes = surface.getNodes();
    List<VolatilityNodeValueView> nodeValues =
        nodes.stream()
            .map(n -> toNodeValueViewForXva(marketData, n.getAtmKey(surface.index())).apply(n))
            .filter(Objects::nonNull)
            .toList();
    Set<String> tenors =
        nodes.stream().map(VolatilitySurfaceNode::getTenor).collect(Collectors.toSet());
    Set<String> expiry =
        nodes.stream().map(VolatilitySurfaceNode::getExpiry).collect(Collectors.toSet());
    if (tenors.size() * expiry.size() > nodes.size()) {
      throw new IllegalArgumentException(
          "IR volatility matrix is incomplete for surface: " + surface.getName());
    } else if (nodeValues.size() != nodes.size()) {
      throw new IllegalArgumentException(
          "Market data is missing for IR volatility matrix. " + "Surface: " + surface.getName());
    }
    return toSwaptionVolatilitiesValue(surface, nodeValues);
  }

  public SwaptionVolatilitiesValue toSwaptionVolatilitiesValue(
      VolatilitySurface surface, List<VolatilityNodeValueView> nodeValues) {
    var convention = surface.swapConvention();
    var floatingEventsPerYear = floatingLegEventsPerYear(convention.getFloatingLeg());
    VolatilityMatrixValues matrix = configurationFromList(nodeValues, surface.getValidFrom());
    return new SwaptionVolatilitiesValue(
        toIntExact(convention.getFixedLeg().getAccrualFrequency().eventsPerYear()),
        toIntExact(floatingEventsPerYear),
        toXvaDayCount(convention.getFixedLeg().getDayCount()),
        toXvaDayCount(convention.getFloatingLeg().getDayCount()),
        matrix.getRows(),
        matrix.getColumns(),
        matrix.matrix());
  }

  private static long floatingLegEventsPerYear(FloatRateSwapLegConvention convention) {
    if (convention instanceof OvernightRateSwapLegConvention overnightConvention) {
      return overnightConvention.getAccrualFrequency().eventsPerYear();
    } else if (convention instanceof IborRateSwapLegConvention iborConvention) {
      return iborConvention.getAccrualFrequency().eventsPerYear();
    } else {
      throw new IllegalArgumentException("Unhandled convention type: " + convention.toString());
    }
  }

  public CreditValue creditValueOf(CreditCurve curveVersioned, MarketData marketData) {
    return new CreditValue(
        curveVersioned.getCorpTicker(),
        curveVersioned.getRecoveryRate(),
        curveVersioned.getCdsNodes().stream()
            .map(CreditCurveCdsNode::getTenor)
            .collect(toImmutableList()),
        curveVersioned.getCdsNodes().stream()
            .map(n -> n.getKey(curveVersioned))
            .map(MarketDataUtils::quoteId)
            .map(n -> marketData.findValue(n).orElse(0d))
            .collect(toImmutableList()));
  }

  public DiscountFactorsValue ofFunding(
      Currency currency, RatesProvider ratesProvider, LocalDate endDate) {
    List<Double> dfs =
        createScheduleDates(ratesProvider, endDate).stream()
            .map(d -> ratesProvider.discountFactor(currency, d))
            .toList();

    return new DiscountFactorsValue(createScheduleDates(ratesProvider, endDate), dfs, null);
  }

  public DiscountFactorsValue ofIndex(
      IborIndex index, ImmutableRatesProvider ratesProvider, LocalDate endDate) {
    var curve = ratesProvider.getIndexCurves().get(index);
    var discountFactors =
        DiscountFactors.of(index.getCurrency(), ratesProvider.getValuationDate(), curve);
    List<Double> dfs =
        createScheduleDates(ratesProvider, endDate).stream()
            .map(discountFactors::discountFactor)
            .toList();

    String floatingLegType = CalculationType.IBOR.getXvaLabel();

    return new DiscountFactorsValue(
        createScheduleDates(ratesProvider, endDate), dfs, floatingLegType);
  }

  private List<LocalDate> createScheduleDates(RatesProvider ratesProvider, LocalDate endDate) {
    return PeriodicSchedule.of(
            ratesProvider.getValuationDate(),
            endDate,
            Frequency.P1M,
            BusinessDayAdjustment.NONE,
            StubConvention.SHORT_FINAL,
            false)
        .createSchedule(referenceData)
        .getPeriods()
        .stream()
        .map(SchedulePeriod::getStartDate)
        .toList();
  }

  private static Map<CurrencyPair, Double> baseCcySelfRate(Currency baseCcy) {
    return Map.of(CurrencyPair.of(baseCcy, baseCcy), BigDecimal.ONE.doubleValue());
  }

  public IRBasisSpreadsValue irBasisSpreadsOf(Currency currency) {
    // For XVA 4.0, IR basis spreads are typically zero or minimal
    // This provides default values that satisfy validation requirements
    List<String> names = List.of("3M6M", "6M12M");
    List<Double> spreads = List.of(0.0, 0.0);
    return new IRBasisSpreadsValue(names, spreads);
  }
}
