<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <module name="solum-xplain-api.xplain-api.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.app.ApiApplication" />
    <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndResources" />
    <option name="VM_PARAMETERS" value="-Duser.timezone=UTC -Dspring.profiles.active=dev,local " />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.solum.xplain.app.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>